import pygame
import sys
import random
import math
import os

# تهيئة Pygame
pygame.init()

# إعدادات الشاشة
WIDTH, HEIGHT = 800, 600
GRID_SIZE = 20
GRID_WIDTH = WIDTH // GRID_SIZE
GRID_HEIGHT = HEIGHT // GRID_SIZE
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("لعبة الأفعى الحديثة")

# الألوان
BACKGROUND = (15, 20, 25)
GRID_COLOR = (30, 35, 40)
SNAKE_HEAD_COLOR = (50, 180, 70)
SNAKE_BODY_COLOR = (40, 160, 60)
APPLE_COLOR = (220, 60, 50)
TEXT_COLOR = (220, 220, 220)
GAME_OVER_BG = (0, 0, 0, 180)

# إعدادات اللعبة
FPS = 12
FONT = pygame.font.SysFont("Arial", 24, bold=True)
BIG_FONT = pygame.font.SysFont("Arial", 48, bold=True)

# فئة الأفعى
class Snake:
    def __init__(self):
        self.reset()
        
    def reset(self):
        self.length = 3
        self.positions = [(GRID_WIDTH // 2, GRID_HEIGHT // 2)]
        self.direction = random.choice([(0, 1), (0, -1), (1, 0), (-1, 0)])
        self.score = 0
        self.grow_to = 3
        self.speed = FPS
        self.last_move_time = 0
        
    def get_head_position(self):
        return self.positions[0]
    
    def update(self, current_time):
        if current_time - self.last_move_time > 1000 / self.speed:
            self.last_move_time = current_time
            head = self.get_head_position()
            x, y = self.direction
            new_position = (((head[0] + x) % GRID_WIDTH), (head[1] + y) % GRID_HEIGHT)
            
            if new_position in self.positions[1:]:
                return False  # اصطدم بنفسه
                
            self.positions.insert(0, new_position)
            
            if len(self.positions) > self.grow_to:
                self.positions.pop()
                
        return True
    
    def render(self, surface):
        for i, pos in enumerate(self.positions):
            color = SNAKE_HEAD_COLOR if i == 0 else SNAKE_BODY_COLOR
            rect = pygame.Rect(pos[0] * GRID_SIZE, pos[1] * GRID_SIZE, GRID_SIZE, GRID_SIZE)
            
            if i == 0:  # رأس الأفعى
                pygame.draw.rect(surface, color, rect, 0, 7)
                # عيون الأفعى
                eye_size = GRID_SIZE // 5
                dx, dy = self.direction
                
                           # تحديد اتجاه العيونsssssss
                if dx == 1:  # يمين
                    left_eye = (rect.left + GRID_SIZE - eye_size*2, rect.top + eye_size*2)
                    right_eye = (rect.left + GRID_SIZE - eye_size*2, rect.bottom - eye_size*3)
                elif dx == -1:  # يسار
                    left_eye = (rect.left + eye_size, rect.top + eye_size*2)
                    right_eye = (rect.left + eye_size, rect.bottom - eye_size*3)
                elif dy == 1:  # أسفل
                    left_eye = (rect.left + eye_size*2, rect.top + GRID_SIZE - eye_size*2)
                    right_eye = (rect.right - eye_size*3, rect.top + GRID_SIZE - eye_size*2)
                else:  # أعلى
                    left_eye = (rect.left + eye_size*2, rect.top + eye_size)
                    right_eye = (rect.right - eye_size*3, rect.top + eye_size)
                
                pygame.draw.circle(surface, (240, 240, 240), left_eye, eye_size)
                pygame.draw.circle(surface, (240, 240, 240), right_eye, eye_size)
                pygame.draw.circle(surface, (20, 20, 20), left_eye, eye_size//2)
                pygame.draw.circle(surface, (20, 20, 20), right_eye, eye_size//2)
            else:  # جسم الأفعى
                pygame.draw.rect(surface, color, rect, 0, 5)
                
                # تفاصيل الجسم
                if i < len(self.positions) - 1:
                    next_pos = self.positions[i+1]
                    if abs(pos[0] - next_pos[0]) + abs(pos[1] - next_pos[1]) > 1:
                        pygame.draw.rect(surface, (color[0]//2, color[1]//2, color[2]//2), rect, 1, 5)

# فئة التفاحة
class Apple:
    def __init__(self):
        self.position = (0, 0)
        self.randomize_position()
        
    def randomize_position(self):
        self.position = (random.randint(0, GRID_WIDTH - 1), random.randint(0, GRID_HEIGHT - 1))
    
    def render(self, surface):
        rect = pygame.Rect(self.position[0] * GRID_SIZE, self.position[1] * GRID_SIZE, GRID_SIZE, GRID_SIZE)
        pygame.draw.circle(surface, APPLE_COLOR, rect.center, GRID_SIZE // 2 - 2)
        
        # تفاصيل التفاحة
        stem_rect = pygame.Rect(rect.centerx - 2, rect.top, 4, 5)
        pygame.draw.rect(surface, (100, 70, 20), stem_rect, 0, 2)
        
        leaf_pos = (rect.centerx + 5, rect.top + 3)
        pygame.draw.ellipse(surface, (50, 180, 50), (leaf_pos[0], leaf_pos[1], 8, 5))

# وظيفة لرسم الشبكة
def draw_grid(surface):
    for y in range(0, HEIGHT, GRID_SIZE):
        for x in range(0, WIDTH, GRID_SIZE):
            rect = pygame.Rect(x, y, GRID_SIZE, GRID_SIZE)
            pygame.draw.rect(surface, GRID_COLOR, rect, 1)

# وظيفة لعرض النقاط
def draw_score(surface, score, high_score):
    score_text = FONT.render(f"النقاط: {score}", True, TEXT_COLOR)
    high_score_text = FONT.render(f"أفضل نتيجة: {high_score}", True, TEXT_COLOR)
    surface.blit(score_text, (10, 10))
    surface.blit(high_score_text, (WIDTH - high_score_text.get_width() - 10, 10))

# وظيفة لعرض شاشة البداية
def draw_start_screen(surface):
    overlay = pygame.Surface((WIDTH, HEIGHT), pygame.SRCALPHA)
    overlay.fill((0, 0, 0, 200))
    surface.blit(overlay, (0, 0))
    
    title = BIG_FONT.render("لعبة الأفعى الحديثة", True, (50, 200, 50))
    surface.blit(title, (WIDTH // 2 - title.get_width() // 2, HEIGHT // 3))
    
    instructions = FONT.render("اضغط على أي زر للبدء", True, TEXT_COLOR)
    surface.blit(instructions, (WIDTH // 2 - instructions.get_width() // 2, HEIGHT // 2))
    
    controls = FONT.render("استخدم مفاتيح الأسهم للتحكم", True, TEXT_COLOR)
    surface.blit(controls, (WIDTH // 2 - controls.get_width() // 2, HEIGHT // 2 + 50))

# وظيفة لعرض شاشة النهاية
def draw_game_over(surface, score):
    overlay = pygame.Surface((WIDTH, HEIGHT), pygame.SRCALPHA)
    overlay.fill(GAME_OVER_BG)
    surface.blit(overlay, (0, 0))
    
    game_over = BIG_FONT.render("انتهت اللعبة!", True, (220, 50, 50))
    surface.blit(game_over, (WIDTH // 2 - game_over.get_width() // 2, HEIGHT // 3))
    
    score_text = FONT.render(f"النقاط النهائية: {score}", True, TEXT_COLOR)
    surface.blit(score_text, (WIDTH // 2 - score_text.get_width() // 2, HEIGHT // 2))
    
    restart = FONT.render("اضغط على R للعب مجدداً", True, (100, 200, 100))
    surface.blit(restart, (WIDTH // 2 - restart.get_width() // 2, HEIGHT // 2 + 60))

# الوظيفة الرئيسية
def main():
    clock = pygame.time.Clock()
    snake = Snake()
    apple = Apple()
    high_score = 0
    
    game_state = "START"  # START, PLAYING, GAME_OVER
    
    # التأكد من أن التفاحة ليست داخل الأفعى
    while apple.position in snake.positions:
        apple.randomize_position()
    
    # حلقة اللعبة الرئيسية
    while True:
        current_time = pygame.time.get_ticks()
        
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            
            if event.type == pygame.KEYDOWN:
                if game_state == "START":
                    game_state = "PLAYING"
                
                elif game_state == "PLAYING":
                    if event.key == pygame.K_UP and snake.direction != (0, 1):
                        snake.direction = (0, -1)
                    elif event.key == pygame.K_DOWN and snake.direction != (0, -1):
                        snake.direction = (0, 1)
                    elif event.key == pygame.K_LEFT and snake.direction != (1, 0):
                        snake.direction = (-1, 0)
                    elif event.key == pygame.K_RIGHT and snake.direction != (-1, 0):
                        snake.direction = (1, 0)
                
                elif game_state == "GAME_OVER":
                    if event.key == pygame.K_r:
                        snake.reset()
                        apple.randomize_position()
                        game_state = "PLAYING"
        
        # تحديث حالة اللعبة
        if game_state == "PLAYING":
            # تحديث الأفعى
            if not snake.update(current_time):
                game_state = "GAME_OVER"
                high_score = max(high_score, snake.score)
            
            # التحقق من أكل التفاحة
            if snake.get_head_position() == apple.position:
                snake.grow_to += 1
                snake.score += 10
                snake.speed = min(snake.speed + 0.5, FPS * 2)  # زيادة السرعة حتى حد معين
                
                # وضع تفاحة جديدة
                apple.randomize_position()
                while apple.position in snake.positions:
                    apple.randomize_position()
        
        # الرسم
        screen.fill(BACKGROUND)
        draw_grid(screen)
        apple.render(screen)
        snake.render(screen)
        draw_score(screen, snake.score, high_score)
        
        if game_state == "START":
            draw_start_screen(screen)
        elif game_state == "GAME_OVER":
            draw_game_over(screen, snake.score)
        
        pygame.display.update()
        clock.tick(60)

if __name__ == "__main__":
    main()