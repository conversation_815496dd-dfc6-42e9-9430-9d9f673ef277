import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Play } from "lucide-react";

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-purple-900 to-black text-white">
      <nav className="flex items-center justify-between p-4 bg-black bg-opacity-80">
        <div className="text-2xl font-bold text-fuchsia-500">ASPHALT LEGENDS UNITE</div>
        <ul className="flex space-x-6 text-white font-medium">
          <li className="underline text-fuchsia-400">HOME</li>
          <li>NEWS</li>
          <li>CREATORS</li>
          <li>ESPORTS</li>
          <li>REDEEM CODE</li>
          <li>SUPPORT</li>
          <li>GAMELOFT CLUB</li>
        </ul>
        <Button className="bg-yellow-400 text-black font-bold">DOWNLOAD</Button>
      </nav>

      <div className="text-center py-24 px-4">
        <h1 className="text-4xl md:text-5xl font-extrabold">
          REV UP FOR OUR FIRST-EVER EXPANSION!
        </h1>
        <h2 className="text-3xl mt-4 font-bold text-fuchsia-500">
          UNITE IN RACING TODAY.
        </h2>
        <div className="mt-6 flex flex-col md:flex-row justify-center gap-4">
          <Button className="bg-yellow-400 text-black font-bold text-lg">
            <Play className="mr-2" size={20} /> WATCH TRAILER
          </Button>
          <Button className="bg-white text-black font-bold text-lg">
            EXPLORE NOW
          </Button>
        </div>
      </div>

      <div className="bg-fuchsia-900 bg-opacity-20 py-8 px-4">
        <h3 className="text-xl font-semibold text-center mb-4">AVAILABLE ON</h3>
        <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-200">
          {[
            "App Store", "Google Play", "Windows", "Steam", "PS5", "PS4",
            "Xbox Series X|S", "Xbox One", "Nintendo Switch", "Galaxy Store",
            "AppGallery", "Google Play Games", "Chromebook", "Game Pass", "Epic Games Store"
          ].map((platform) => (
            <Card key={platform} className="bg-black bg-opacity-50 border border-fuchsia-600 p-2 rounded-xl">
              <CardContent className="text-center font-medium">{platform}</CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
